<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';

	import FilterPanel from './FilterPanel.svelte';
	import type { CustomerPlatformIdentity, Message } from '$lib/types/customer';
	// import { platformWebSocket } from '$lib/websocket/platformWebSocket'; // No longer needed - handled by +page.svelte
	import { conversationStore } from '$lib/stores/conversationStore';
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import {
		AdjustmentsHorizontalSolid,
		UserHeadsetOutline,
		BullhornOutline,
		TicketSolid,
		SearchOutline,
		ExclamationCircleOutline,
		ArrowDownOutline
	} from 'flowbite-svelte-icons';
	import { Badge, Tooltip, Button } from 'flowbite-svelte';
	import { getInitials } from '$lib/utils/avatarGenerator';

	import { tabCacheStore } from '$lib/stores/tabCacheStore';
	import { selectedTicketData } from '$lib/stores/ticketStore';

	export let platformIdentities: CustomerPlatformIdentity[] = [];
	export let selectedPlatformId: number | null = null;
	export let hasMore: boolean = false;
	export let loading: boolean = false;
	export let activeTab: string = 'my-assigned';
	export let currentUsername: string = '';

	const dispatch = createEventDispatcher();

	/**
	 * Get the currently active platform filter from filterData
	 */
	// function getActivePlatformFilter(): string | undefined {
	// 	if (filterData.platforms && !filterData.platforms.has('All')) {
	// 		// Return the first selected platform (assuming single selection for now)
	// 		const selectedPlatforms = Array.from(filterData.platforms);
	// 		return selectedPlatforms.length > 0 ? selectedPlatforms[0] : undefined;
	// 	}
	// 	return undefined;
	// }

	let searchTerm = '';
	let isSearching = false;
	let latestMessages: Map<number, Message> = new Map();
	let unreadCounts: Map<number, number> = new Map();

	// Change detection for automatic updates
	// let previousPlatformIdentities: any[] = [];
	// let previousLatestTicketIds = new Map<number, any>();

	// Avatar image error tracking
	let imageErrors = new Set<number>();

	// Tab state is now managed by parent component via activeTab prop

	// Tab definitions
	const tabs = [
		{ id: 'my-assigned', label: 'My Assigned', key: 'chat_center_filter_tab_my_assigned' },
		{ id: 'my-closed', label: 'My Closed', key: 'chat_center_filter_tab_my_closed' },
		{ id: 'open', label: 'Open', key: 'chat_center_filter_tab_open' },
		{ id: 'others-assigned', label: 'Others Assigned', key: 'chat_center_filter_tab_all_assigned' }
	];

	// Filter state (sorting is now handled server-side)
	let showFilterPanel = false;
	let activeFiltersCount = 0;
	let filterData = {
		dateRange: null,
		customStartDate: null,
		customEndDate: null,
		platforms: new Set(['All']),
		channels: new Set(['All']),
		unreadFilter: new Set(['All']),
		statuses: new Set(['All']),
		tags: new Set(['All']),
		owners: new Set(['All']),
		priorities: new Set(['All']),
		searchText: ''
	};

	// Automatic tab switching state
	let previousSelectedStatus: string | null = null;
	let previousSelectedOwner: string | null = null;
	let isInitialLoad = true;

	// Server-side filtering is now used, so we only need to sort the results
	$: filteredIdentities = filterIdentities(platformIdentities, searchTerm, filterData);
	$: sortedIdentities = sortIdentities(filteredIdentities, latestMessages);

	// Subscribe to store changes for reactive tab switching
	$: storeSelectedPlatformId = $tabCacheStore?.selectedPlatformId;
	$: storeSelectedTicketData = $selectedTicketData;

	// Debug logging for platform data changes
	// $: {
	// 	console.log('[DEBUG] PlatformIdentityList: Platform data updated', {
	// 		activeTab,
	// 		platformIdentitiesCount: platformIdentities.length,
	// 		platformIdentitiesIds: platformIdentities.map(p => p.id),
	// 		filteredIdentitiesCount: filteredIdentities.length,
	// 		sortedIdentitiesCount: sortedIdentities.length,
	// 		sortedIdentitiesIds: sortedIdentities.map(p => p.id),
	// 		loading,
	// 		hasMore
	// 	});

	// 	// Check for duplicate IDs in platformIdentities (input data)
	// 	if (platformIdentities.length > 0) {
	// 		const ids = platformIdentities.map(p => p.id);
	// 		const uniqueIds = [...new Set(ids)];
	// 		if (ids.length !== uniqueIds.length) {
	// 			console.error('[ERROR] PlatformIdentityList: Duplicate platform IDs detected in platformIdentities prop:', {
	// 				activeTab,
	// 				allIds: ids,
	// 				duplicates: ids.filter((id, index) => ids.indexOf(id) !== index),
	// 				platformsWithDuplicateIds: platformIdentities.filter((p, index) => ids.indexOf(p.id) !== index)
	// 			});
	// 		}
	// 	}

	// 	// Check for duplicate IDs in sortedIdentities (what gets rendered)
	// 	if (sortedIdentities.length > 0) {
	// 		const sortedIds = sortedIdentities.map(p => p.id);
	// 		const uniqueSortedIds = [...new Set(sortedIds)];
	// 		if (sortedIds.length !== uniqueSortedIds.length) {
	// 			console.error('[ERROR] PlatformIdentityList: Duplicate platform IDs detected in sortedIdentities (KEYED EACH WILL FAIL):', {
	// 				activeTab,
	// 				allIds: sortedIds,
	// 				duplicates: sortedIds.filter((id, index) => sortedIds.indexOf(id) !== index),
	// 				platformsWithDuplicateIds: sortedIdentities.filter((p, index) => sortedIds.indexOf(p.id) !== index)
	// 			});
	// 		}
	// 	}
	// }

	// Handle search with tabCacheStore coordination (with debouncing)
	let searchTimeout: ReturnType<typeof setTimeout>;

	function delayedSearch() {
		if (searchTimeout) clearTimeout(searchTimeout);
		searchTimeout = setTimeout(async () => {
			// Only trigger search operations if there's actual search content
			// or if we need to clear a previous search (similar to users page pattern)
			if (searchTerm.trim() || searchTerm === '') {
				isSearching = true;
				// console.log('[DEBUG] PlatformIdentityList: Search term changed, updating tabCacheStore:', searchTerm);

				try {
					// Stop polling temporarily during search
					tabCacheStore.stopPolling();

					// Update search term and refresh data
					await tabCacheStore.updateSearchAndRefresh(searchTerm);

					// Resume polling with the new search filter
					tabCacheStore.startPolling();
				} catch (error) {
					console.error('Search error:', error);
				} finally {
					isSearching = false;
				}
			}
		}, 500);
	}

	$: searchTerm, delayedSearch();

	// Clear search function
	function clearSearch() {
		searchTerm = '';
		// The reactive statement will handle the actual clearing
	}

	// Helper function for dynamic input padding
	function getInputPaddingRight() {
		if (isSearching || searchTerm) {
			return 'pr-10';
		}
		return 'pr-3';
	}

	// Automatic tab switching logic based on status and ownership changes
	$: {
		// Ensure reactivity by referencing store values
		const currentStoreSelectedPlatformId = storeSelectedPlatformId;
		const currentStoreTicketData = storeSelectedTicketData;
		
		if (currentStoreSelectedPlatformId && currentStoreTicketData && platformIdentities.length > 0 && currentUsername) {
			// Get status and owner from store data
			let currentStatus = currentStoreTicketData.ticket.status;
			let currentOwner = currentStoreTicketData.ticket.owner.username;

			// console.log('[DEBUG] PlatformIdentityList: Selected platform data:', {
			// 	platformId: currentStoreSelectedPlatformId,
			// 	status: currentStatus,
			// 	owner: currentOwner,
			// 	currentUser: currentUsername
			// });

			// Only trigger tab switching on actual changes, not initial load
			if (!isInitialLoad &&
				(currentStatus !== previousSelectedStatus || currentOwner !== previousSelectedOwner)) {

				// Apply automatic tab switching rules
				let newTab = activeTab; // Default to current tab

				// Rule 1: Switch to 'my-assigned' when status becomes 'assigned' OR 'pending_to_close' AND owner is current user
				if ((currentStatus === 'assigned' || currentStatus === 'pending_to_close') &&
					currentOwner === currentUsername) {
					newTab = 'my-assigned';
				}
				// Rule 2: Switch to 'my-closed' when status becomes 'closed' AND owner is current user
				else if (currentStatus === 'closed' && currentOwner === currentUsername) {
					newTab = 'my-closed';
				}
				// Rule 3: Switch to 'open' when status becomes 'open' (regardless of owner)
				else if (currentStatus === 'open') {
					newTab = 'open';
				}
				// Rule 4: Switch to 'others-assigned' when owner changes to someone else AND status is NOT 'open'
				else if (currentOwner !== currentUsername && currentOwner != null && currentStatus !== 'open') {
					newTab = 'others-assigned';
				}

				// Only switch if the new tab is different from current tab
				if (newTab !== activeTab) {
					// console.log(`Auto-switching from '${activeTab}' to '${newTab}' due to status/owner change:`, {
					// 	previousStatus: previousSelectedStatus,
					// 	currentStatus,
					// 	previousOwner: previousSelectedOwner,
					// 	currentOwner,
					// 	currentUser: currentUsername
					// });
					dispatch('tabChange', { tab: newTab });
				}
			}

			// Update tracking variables
			previousSelectedStatus = currentStatus;
			previousSelectedOwner = currentOwner;

			// Mark that initial load is complete
			if (isInitialLoad) {
				isInitialLoad = false;
			}
		}
	}

	// Reset tracking variables when selected platform changes
	$: if (selectedPlatformId) {
		// Reset tracking state for new selection
		isInitialLoad = true;
		previousSelectedStatus = null;
		previousSelectedOwner = null;
	}

	// let isComponentDestroyed = false;

	onMount(() => {
		// Only access window in browser environment
		// if (typeof window !== 'undefined') {
			// Listen for WebSocket events for real-time updates
			window.addEventListener('platform-new-message', handleNewMessage as EventListener);
			window.addEventListener('platform-status-update', handleStatusUpdate as EventListener);
			window.addEventListener('platform-typing', handleTypingIndicator as EventListener);
			window.addEventListener('platform-bulk-update', handleBulkUpdate as EventListener);
			window.addEventListener('platform-batch-complete', handleBatchComplete as EventListener);

			// No longer needed - handled by +page.svelte
			// Subscribe to all visible platforms for real-time updates
			// const visiblePlatformIds = platformIdentities.map((p) => p.id);
			// if (visiblePlatformIds.length > 0) {
			// 	console.log('Subscribing to platforms:', visiblePlatformIds);
			// 	platformWebSocket.subscribeToMultiplePlatforms(visiblePlatformIds);
			// }
		// }
	});

	onDestroy(() => {
		// isComponentDestroyed = true;

		// Clean up search timeout
		if (searchTimeout) {
			clearTimeout(searchTimeout);
		}

		// Stop polling service first
		// stopPlatformPolling();

		// Unsubscribe from WebSocket platforms
		// if (typeof window !== 'undefined') {
			// const visiblePlatformIds = platformIdentities.map((p) => p.id);
			// if (visiblePlatformIds.length > 0) {
			// 	platformWebSocket.unsubscribeFromMultiplePlatforms(visiblePlatformIds);
			// }

			// Clean up event listeners
			window.removeEventListener('platform-new-message', handleNewMessage as EventListener);
			window.removeEventListener('platform-status-update', handleStatusUpdate as EventListener);
			window.removeEventListener('platform-typing', handleTypingIndicator as EventListener);
			window.removeEventListener('platform-bulk-update', handleBulkUpdate as EventListener);
			window.removeEventListener('platform-batch-complete', handleBatchComplete as EventListener);
		// }
	});

	// loadAdditionalData function removed - now handled directly in fetchPlatformDataForPolling

	// Note: Polling is now handled by the tabCacheStore in the parent component
	// This component no longer needs its own polling mechanism

	/**
	 * Handle polling data updates and detect changes
	 */
	// function handlePollingDataUpdate(): void {
	// 	// Check if component is still mounted
	// 	if (isComponentDestroyed) {
	// 		return;
	// 	}

	// 	// Detect changes in platform identities after the data refresh
	// 	detectAndHandlePlatformChanges();

	// 	// Check for System-owned closed tickets with unread messages and mark them as read
	// 	checkAndMarkSystemClosedTicketsAsRead();
	// }

	/**
	 * Detect changes in platform identity data and dispatch events for automatic updates
	 */
	// function detectAndHandlePlatformChanges(): void {
	// 	try {
	// 		// Early exit if component is destroyed
	// 		if (isComponentDestroyed) {
	// 			return;
	// 		}

	// 		// Check if we have previous data to compare against
	// 		if (previousPlatformIdentities.length === 0) {
	// 			// First time - store current data as baseline
	// 			storePlatformIdentitiesSnapshot();
	// 			return;
	// 		}

	// 		// Detect changes in latest_ticket_id for each platform
	// 		const changedPlatforms: any[] = [];

	// 		platformIdentities.forEach((currentIdentity) => {
	// 			const currentLatestTicketId = (currentIdentity as any)['latest_ticket_id'];
	// 			const previousLatestTicketId = previousLatestTicketIds.get(currentIdentity.id);

	// 			// Check if latest_ticket_id has changed
	// 			if (currentLatestTicketId !== previousLatestTicketId) {
	// 				// console.log(`PlatformIdentityList.svelte: Detected ticket ID change for platform ${currentIdentity.id}:`, {
	// 				// 	previous: previousLatestTicketId,
	// 				// 	current: currentLatestTicketId,
	// 				// 	platformId: currentIdentity.id
	// 				// });

	// 				changedPlatforms.push({
	// 					platformIdentity: currentIdentity,
	// 					previousTicketId: previousLatestTicketId,
	// 					currentTicketId: currentLatestTicketId
	// 				});
	// 			}
	// 		});

	// 		// If there are changes, dispatch events for automatic updates
	// 		if (changedPlatforms.length > 0) {
	// 			handleAutomaticPlatformUpdates(changedPlatforms);
	// 		}

	// 		// Update the snapshot for next comparison
	// 		storePlatformIdentitiesSnapshot();

	// 	} catch (error) {
	// 		if (!isComponentDestroyed) {
	// 			console.error('PlatformIdentityList.svelte: Error detecting platform changes:', error);
	// 		}
	// 	}
	// }

	/**
	 * Store current platform identities data for change detection
	 */
	// function storePlatformIdentitiesSnapshot(): void {
	// 	// Store a deep copy of current platform identities
	// 	previousPlatformIdentities = JSON.parse(JSON.stringify(platformIdentities));

	// 	// Store latest_ticket_id for each platform
	// 	previousLatestTicketIds.clear();
	// 	platformIdentities.forEach((identity) => {
	// 		const latestTicketId = (identity as any)['latest_ticket_id'];
	// 		previousLatestTicketIds.set(identity.id, latestTicketId);
	// 	});
	// }

	/**
	 * Handle automatic platform updates when changes are detected
	 */
	// function handleAutomaticPlatformUpdates(changedPlatforms: any[]): void {
	// 	try {
	// 		// For each changed platform, check if it's the currently selected one
	// 		changedPlatforms.forEach(({ platformIdentity, previousTicketId, currentTicketId }) => {
	// 			// If this is the currently selected platform, trigger automatic update
	// 			if (selectedPlatformId === platformIdentity.id) {
	// 				// console.log('PlatformIdentityList.svelte: Auto-updating selected platform due to ticket change:', {
	// 				// 	platformId: platformIdentity.id,
	// 				// 	previousTicketId,
	// 				// 	currentTicketId
	// 				// });

	// 				// Dispatch platform update event to parent
	// 				dispatch('platformUpdate', {
	// 					platformId: platformIdentity.id,
	// 					customerId: extractCustomerId(platformIdentity),
	// 					platformIdentity: platformIdentity,
	// 					changeType: 'ticket_id_change',
	// 					previousTicketId,
	// 					currentTicketId,
	// 					isAutoUpdate: true
	// 				});
	// 			}
	// 		});

	// 		// Also dispatch a general change notification for all changed platforms
	// 		dispatch('platformDataChanged', {
	// 			changedPlatforms,
	// 			timestamp: new Date().toISOString()
	// 		});

	// 	} catch (error) {
	// 		console.error('PlatformIdentityList.svelte: Error handling automatic platform updates:', error);
	// 	}
	// }

	/**
	 * Handle polling errors
	 */
	// function handlePollingError(error: Error): void {
	// 	console.error('PlatformIdentityList.svelte: Polling error:', error);
	// 	// Don't show toast for polling errors to avoid spam
	// }

	// Note: Polling is now handled by tabCacheStore in the parent component

	// loadLatestIdentities function removed - now handled by tabCacheStore.updateSearchAndRefresh()

	// Removed - now handled by polling in tabCacheStore
	// async function loadLatestMessages(platformIds: number[]) {
	// 	try {
	// 		if (!access_token) {
	// 			console.error('No access token available');
	// 			return;
	// 		}

	// 		const result = await customerService.getLatestMessages(platformIds, access_token);

	// 		if (result.res_status === 200) {
	// 			latestMessages = new Map(Object.entries(result.messages).map(([k, v]) => [parseInt(k), v as Message]));
	// 		} else {
	// 			console.error('Error loading latest messages:', result.error_msg);
	// 		}
	// 	} catch (error) {
	// 		console.error('Error loading latest messages:', error);
	// 	}
	// }

	// Removed - now handled by polling in tabCacheStore
	// async function loadUnreadCounts(platformIds: number[]) {
	// 	try {
	// 		if (!access_token) {
	// 			console.error('No access token available');
	// 			return;
	// 		}

	// 		const result = await customerService.getUnreadCounts(platformIds, access_token);

	// 		if (result.res_status === 200) {
	// 			unreadCounts = new Map(Object.entries(result.unread_counts).map(([k, v]) => [parseInt(k), v as number]));
	// 			// console.log('PlatformIdentityList.svelte: Unread counts loaded:', result.unread_counts);
	// 		} else {
	// 			console.error('Error loading unread counts:', result.error_msg);
	// 		}
	// 	} catch (error) {
	// 		console.error('Error loading unread counts:', error);
	// 	}
	// }

	// TODO: This must be done at server-side
	// async function markAllMessagesAsRead(customerId: number, platformId: number) {
	// 	try {
	// 		if (!access_token) {
	// 			console.error('No access token available');
	// 			return;
	// 		}

	// 		const result = await customerService.markAllMessagesAsRead(customerId, platformId, access_token);

	// 		if (result.res_status !== 200) {
	// 			console.error('PlatformIdentityList: Error marking all messages as read:', result.error_msg);
	// 		}
	// 	} catch (error) {
	// 		console.error('PlatformIdentityList: Error marking all messages as read:', error);
	// 	}
	// }

	// TODO: This must be done at server-side
	/**
	 * Check for System-owned closed tickets with unread messages and mark them as read
	 */
	// async function checkAndMarkSystemClosedTicketsAsRead(): Promise<void> {
	// 	try {
	// 		// Find platform identities that match the criteria
	// 		const systemClosedTicketsWithUnread = platformIdentities.filter((identity) => {
	// 			const ticketOwner = (identity as any)['latest_ticket_owner'];
	// 			const ticketStatus = (identity as any)['latest_ticket_status'];
	// 			const unreadCount = unreadCounts.get(identity.id) || 0;

	// 			return (
	// 				ticketOwner === 'System Bot' &&
	// 				ticketStatus?.toLowerCase() === 'closed' &&
	// 				unreadCount > 0
	// 			);
	// 		});

	// 		// Process each matching identity
	// 		for (const identity of systemClosedTicketsWithUnread) {
	// 			try {
	// 				// Extract customer ID using existing helper function
	// 				const customerId = extractCustomerId(identity);
	// 				if (customerId === 0) {
	// 					console.warn('PlatformIdentityList: Invalid customer ID for platform identity:', identity.id);
	// 					continue;
	// 				}

	// 				// console.log(
	// 				// 	`PlatformIdentityList: Auto-marking messages as read for System-owned closed ticket: Platform ${identity.id}, Customer ${customerId}, Unread count: ${unreadCounts.get(identity.id)}`
	// 				// );

	// 				// Mark all messages as read
	// 				await markAllMessagesAsRead(customerId, identity.id);

	// 				// Update local unread count to 0
	// 				unreadCounts.set(identity.id, 0);
	// 				unreadCounts = unreadCounts; // Trigger reactivity
	// 			} catch (error) {
	// 				console.error(
	// 					`PlatformIdentityList: Error processing System-owned closed ticket for platform ${identity.id}:`,
	// 					error
	// 				);
	// 			}
	// 		}
	// 	} catch (error) {
	// 		console.error('PlatformIdentityList: Error in checkAndMarkSystemClosedTicketsAsRead:', error);
	// 	}
	// }

	function filterIdentities(identities: CustomerPlatformIdentity[], search: string, filters: any) {
		let filtered = identities;

		// Apply search filter from main search input
		if (search) {
			const searchLower = search.toLowerCase();
			filtered = filtered.filter(
				(p) => {
					const extendedP = p as any; // Server response includes additional fields
					return (
						// Search in platform username
						p.platform_username?.toLowerCase().includes(searchLower) ||
						// Search in channel name
						p.channel_name?.toLowerCase().includes(searchLower) ||
						// Search in ticket ID
						extendedP.latest_ticket_id?.toString().includes(searchLower) ||
						// Search in ticket owner
						extendedP.latest_ticket_owner?.toLowerCase().includes(searchLower) ||
						// Search in customer's fullname
						extendedP.customer_fullname?.toLowerCase().includes(searchLower) ||
						// Search in customer's citizen ID
						extendedP.customer_national_id?.includes(searchLower)
					);
				}
			);
		}

		// Apply platform filter
		if (filters.platforms && !filters.platforms.has('All')) {
			filtered = filtered.filter((p) => filters.platforms.has(p.platform));
		}

		// Apply channel filter
		if (filters.channels && !filters.channels.has('All')) {
			filtered = filtered.filter((p) => {
				const channelName = p.channel_name || 'No Channel';
				return filters.channels.has(channelName);
			});
		}

		// Apply unread messages filter
		if (filters.unreadFilter && !filters.unreadFilter.has('All')) {
			filtered = filtered.filter((p) => {
				const unreadCount = unreadCounts.get(p.id) || p.unread_count || 0;

				if (filters.unreadFilter.has('unread')) {
					return unreadCount > 0;
				}
				return true;
			});
		}

		// Apply date range filter
		if (filters.dateRange) {
			const now = new Date();
			let startDate: Date | undefined;

			switch (filters.dateRange) {
				case 'today':
					startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
					break;
				case 'yesterday':
					startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
					break;
				case 'week':
					startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
					break;
				case 'month':
					startDate = new Date(now.getFullYear(), now.getMonth(), 1);
					break;
				case 'last-3-months':
					startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
					break;
				case 'last-6-months':
					startDate = new Date(now.getFullYear(), now.getMonth() - 6, 1);
					break;
				case 'custom':
					if (filters.customStartDate) {
						startDate = new Date(filters.customStartDate);
					}
					break;
			}

			if (startDate) {
				filtered = filtered.filter((p) => {
					const messageTime =
						latestMessages.get(p.id)?.created_on || p.last_interaction || p.created_on;
					return messageTime && new Date(messageTime) >= startDate;
				});
			}

			// [Unused] Apply custom end date if specified
			// if (filters.dateRange === 'custom' && filters.customEndDate) {
			// 	const endDate = new Date(filters.customEndDate);
			// 	endDate.setHours(23, 59, 59, 999); // End of day
			// 	filtered = filtered.filter((p) => {
			// 		const messageTime =
			// 			latestMessages.get(p.id)?.created_on || p.last_interaction || p.created_on;
			// 		return messageTime && new Date(messageTime) <= endDate;
			// 	});
			// }
		}

		// Apply status filter
		if (filters.statuses && !filters.statuses.has('All')) {
			filtered = filtered.filter((p) => {
				const status = (p as any)['latest_ticket_status'];
				return status && filters.statuses.has(status);
			});
		}

		// Apply owner filter
		if (filters.owners && !filters.owners.has('All')) {
			filtered = filtered.filter((p) => {
				const fullOwner = (p as any)['latest_ticket_owner'];
				if (!fullOwner) return false; // Exclude identities without owners

				// Extract first word/name only to match the filter options
				const ownerFirstName = fullOwner.split(/\s+/)[0];
				return filters.owners.has(ownerFirstName);
			});
		}

		// Apply priority filter
		if (filters.priorities && !filters.priorities.has('All')) {
			filtered = filtered.filter((p) => {
				const priority = (p as any)['latest_ticket_priority'];
				if (!priority) return false; // Exclude identities without priority

				// Check if the priority matches one of the selected priorities
				return filters.priorities.has(priority);
			});
		}

		return filtered;
	}

	function sortIdentities(identities: CustomerPlatformIdentity[], messages: Map<number, Message>) {
		return [...identities].sort((a, b) => {
			const aMsg = messages.get(a.id);
			const bMsg = messages.get(b.id);

			// If both have messages, sort by message time
			if (aMsg && bMsg) {
				return new Date(bMsg.created_on).getTime() - new Date(aMsg.created_on).getTime();
			}

			// If only one has a message, it goes first
			if (aMsg && !bMsg) return -1;
			if (!aMsg && bMsg) return 1;

			// If neither has messages, sort by last interaction
			const aTime = a.last_interaction || a.created_on;
			const bTime = b.last_interaction || b.created_on;
			if (aTime && bTime) {
				return new Date(bTime).getTime() - new Date(aTime).getTime();
			}
			return 0;
		});
	}

	function handleIdentityClick(identity: CustomerPlatformIdentity) {
		// Note: Data refresh is now handled automatically by the polling service
		// No need for manual refresh on click as polling provides up-to-date data

		// Check if customer is just an ID or a full object
		let customerId: number;

		if (typeof identity.customer === 'number') {
			// If customer is just an ID, use it directly
			customerId = identity.customer;
		} else if (
			identity.customer &&
			typeof identity.customer === 'object' &&
			identity.customer.customer_id
		) {
			// If customer is an object with customer_id
			customerId = identity.customer.customer_id;
		} else {
			console.error('Platform identity missing valid customer data:', identity);
			return;
		}

		// Get ticket ID from the identity (server adds this field)
		const ticketId = (identity as any)['latest_ticket_id']?.toString();

		console.log('PlatformIdentityList: Selected identity:', {
			platformId: identity.id,
			customerId,
			ticketId
		});

		// Update the tab cache store with selected platform and ticket
		tabCacheStore.setSelectedPlatform(identity.id, ticketId);

		// Pass the entire identity object along with the extracted customerId and ticketId
		dispatch('select', {
			platformId: identity.id,
			customerId: customerId,
			platformIdentity: identity,
			ticketId: ticketId
		});
	}

	function handleTabClick(newTab: string) {
		if (newTab !== activeTab) {
			dispatch('tabChange', { tab: newTab });
		}
	}

	function handleLoadMore() {
		if (!loading && hasMore) {
			dispatch('loadMore', { tab: activeTab });
		}
	}

	function handleNewMessage(event: CustomEvent) {
		const { platformId, message, unreadCount, updateType, batchId } = event.detail;

		// Enhanced logging for batch broadcasting
		if (batchId) {
			console.log(`Batch message received - Platform: ${platformId}, Batch: ${batchId}, Type: ${updateType}`, message);
		}

		// Update latest message
		latestMessages.set(platformId, message);
		latestMessages = latestMessages; // Trigger reactivity

		// Update unread count
		unreadCounts.set(platformId, unreadCount);
		unreadCounts = unreadCounts; // Trigger reactivity

		// IMPORTANT: Add the message to conversation store
		// This ensures the message appears in the conversation view
		// The conversationStore.addMessage already handles duplicate prevention
		conversationStore.addMessage(platformId, message);

		// Re-sort the list to move updated platform to top
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function handleStatusUpdate(event: CustomEvent) {
		const { platformId } = event.detail;

		// Update platform status if needed
		const platform = platformIdentities.find((p) => p.id === platformId);
		if (platform) {
			// You can add status to your platform type if needed
			platformIdentities = platformIdentities; // Trigger reactivity
		}
	}

	function handleTypingIndicator(event: CustomEvent) {
		// You can implement typing indicator UI here if needed
		console.log('Typing indicator received:', event.detail);
	}

	function handleBulkUpdate(event: CustomEvent) {
		const updates = event.detail;

		// Update multiple platforms at once
		Object.entries(updates).forEach(([platformId, data]: [string, any]) => {
			const id = parseInt(platformId);

			if (data.latest_message) {
				latestMessages.set(id, data.latest_message);
			}

			if (data.unread_count !== undefined) {
				unreadCounts.set(id, data.unread_count);
			}
		});

		// Trigger reactivity
		latestMessages = latestMessages;
		unreadCounts = unreadCounts;
		sortedIdentities = sortIdentities(filteredIdentities, latestMessages);
	}

	function handleBatchComplete(event: CustomEvent) {
		const { batchId, platformId, summary } = event.detail;

		console.log(`Batch ${batchId} completed for platform ${platformId}:`, summary);

		// Optional: You could show a notification or update UI to indicate batch completion
		// For now, we'll just log it as the individual messages have already been processed

		// If you want to show batch completion status in the UI, you could:
		// - Update a batch status indicator
		// - Show a toast notification for failed messages
		// - Update platform status to show batch completion

		if (summary.failed > 0) {
			console.warn(`Batch ${batchId} had ${summary.failed} failed messages out of ${summary.total}`);
			// You could show an error indicator here
		}
	}

	function formatTime(dateString: string): string {
		const lang = get(language); // 'en' or 'th'
		const date = new Date(dateString);
		const now = new Date();
		const diff = now.getTime() - date.getTime();

		// Define localized labels
		const labels = {
			en: {
				justNow: 'Just now',
				minutesAgo: (m: number) => `${m}m ago`,
				hoursAgo: (h: number) => `${h}h ago`,
				daysAgo: (d: number) => `${d}d ago`,
				yesterday: 'Yesterday'
			},
			th: {
				justNow: 'เมื่อสักครู่',
				minutesAgo: (m: number) => `${m} นาทีที่แล้ว`,
				hoursAgo: (h: number) => `${h} ชั่วโมงที่แล้ว`,
				daysAgo: (d: number) => `${d} วันที่แล้ว`,
				yesterday: 'เมื่อวานนี้'
			}
		};

		// Fallback to 'en' if lang is not supported
		const currentLang = ['en', 'th'].includes(lang) ? lang : 'en';
		const l = labels[currentLang as 'en' | 'th'];

		if (diff < 60000) return l.justNow;
		if (diff < 3600000) return l.minutesAgo(Math.floor(diff / 60000));
		if (diff > 86400000 && diff < 172800000) return l.yesterday;
		if (diff < 86400000) return l.hoursAgo(Math.floor(diff / 3600000));
		if (diff < 604800000) return l.daysAgo(Math.floor(diff / 86400000));

		// If more than a week, format as date in local language
		return date.toLocaleDateString(lang === 'th' ? 'th-TH' : 'en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function getPlatformIcon(platform: string): string {
		const icons: Record<string, string> = {
			LINE: '/images/platform-line.png',
			WHATSAPP: '/images/platform-whatsapp.png',
			FACEBOOK: '/images/platform-facebook.png',
			INSTAGRAM: '/images/platform-instagram.png'
		};
		return icons[platform] || '/images/platform-line.png';
	}

	function getCustomerName(identity: CustomerPlatformIdentity): string {
		return identity.platform_username || 'Unknown Customer';
	}

	function handleImageError(identityId: number) {
		imageErrors.add(identityId);
		imageErrors = imageErrors; // Trigger reactivity
	}

	function isValidImageUrl(url: string | undefined): boolean {
		if (!url) return false;
		// Basic URL validation
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}

	// Sort and filter handlers removed - sorting is now handled server-side

	function handleFilter(event: MouseEvent) {
		event.stopPropagation();
		showFilterPanel = !showFilterPanel;
	}

	function handleFilterApply(event: CustomEvent) {
		filterData = event.detail;
		// The reactive statement will automatically update filteredIdentities
	}

	function handleFilterClose() {
		showFilterPanel = false;
	}

	/**
	 * Update unread count for a specific platform
	 * Called from parent component when messages are marked as read
	 */
	export function updateUnreadCount(platformId: number, countChange: number) {
		const currentCount = unreadCounts.get(platformId) || 0;
		const newCount = Math.max(0, currentCount + countChange); // Ensure count doesn't go below 0

		unreadCounts.set(platformId, newCount);
		unreadCounts = unreadCounts; // Trigger reactivity

		// console.log(`PlatformIdentityList: Updated unread count for platform ${platformId}: ${currentCount} -> ${newCount}`);
	}

	// For Debugging
	// $: {
	// 	console.log('PlatformIdentityList.svelte: Variables: platformIdentities:', platformIdentities);
	// 	console.log('PlatformIdentityList.svelte: Variables: filteredIdentities:', filteredIdentities);
	// 	console.log('PlatformIdentityList.svelte: Variables: sortedIdentities:', sortedIdentities);
	// }
</script>

<div
	id="platform-list-platform-identity-list"
	class="flex h-full flex-col"
	data-testid="platform-identity-list"
>
	<!-- Header with Search -->
	<div
		id="platform-list-chat-center-header"
		class="min-h-[120px] border-b border-gray-200 p-4"
		data-testid="chat-center-header"
	>
		<div
			id="platform-list-chat-center-title-container"
			class="mb-3 flex items-center justify-between"
			data-testid="chat-center-title-container"
		>
			<h2
				id="platform-list-chat-center-title"
				class="text-lg font-semibold"
				data-testid="chat-center-title"
			>
				{t('chat_center')}
			</h2>
			<!-- For Debugging -->
			<!-- {#if refreshing}
				<div class="flex items-center text-sm text-blue-600">
					<svg class="mr-2 h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
						></circle>
						<path
							class="opacity-75"
							fill="currentColor"
							d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
						></path>
					</svg>
					Refreshing...
				</div>
			{/if} -->
		</div>
		<div
			id="platform-list-chat-center-search-container"
			class="flex items-center gap-1"
			data-testid="chat-center-search-container"
		>
			<div
				id="platform-list-search-input-container"
				class="relative flex-grow"
				data-testid="search-input-container"
			>
				<input
					id="platform-list-chat-center-search-input"
					type="text"
					bind:value={searchTerm}
					placeholder={t('chat_center_search_placeholder')}
					class="w-full rounded-lg border border-gray-300 px-3 py-2 pl-9 {getInputPaddingRight()} text-sm text-gray-600
                           focus:outline-none focus:ring-2 focus:ring-blue-500"
					aria-label="Search conversations"
					aria-describedby="platform-list-search-tooltip"
					role="searchbox"
					autocomplete="off"
					data-testid="chat-center-search-input"
				/>
				<SearchOutline
					id="platform-list-search-icon"
					class="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
					data-testid="search-icon"
				/>

				<!-- Loading indicator (right side, when searching) -->
				{#if isSearching}
					<div
						id="platform-list-search-loading"
						class="absolute inset-y-0 right-0 pr-3 flex items-center"
						data-testid="search-loading-indicator"
					>
						<div class="h-4 w-4 animate-spin rounded-full border-b-2 border-gray-500"></div>
					</div>
				{/if}

				<!-- Clear button (right side, when has content and not searching) -->
				{#if searchTerm && !isSearching}
					<div
						id="platform-list-search-clear"
						class="absolute inset-y-0 right-0 pr-3 flex items-center"
						data-testid="search-clear-button-container"
					>
						<button
							type="button"
							on:click={clearSearch}
							class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
							aria-label="Clear search"
							data-testid="search-clear-button"
						>
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
							</svg>
						</button>
					</div>
				{/if}

				<Tooltip
					triggeredBy="#platform-list-chat-center-search-input"
					trigger="focus"
					id="platform-list-search-tooltip"
					placement="bottom"
					class="bg-gray-600 text-sm"
					data-testid="search-tooltip"
				>
					{t('chat_center_search_tooltip')}
				</Tooltip>
			</div>
			<!-- Refresh is now handled automatically by polling service -->
			<div
				id="platform-list-filter-container"
				class="relative flex-shrink-0"
				data-testid="filter-container"
			>
				<button
					id="platform-list-filter-button"
					type="button"
					on:click={handleFilter}
					class="flex cursor-pointer items-center justify-center gap-1 rounded-lg border border-gray-300 px-2 py-2 text-sm
                           transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500
                           {showFilterPanel ? 'border-blue-300 bg-blue-50' : activeFiltersCount > 0 ? 'bg-gray-700 text-white border-gray-700 hover:bg-gray-600' : ''}"
					aria-label="Filter conversations"
					aria-expanded={showFilterPanel}
					data-testid="filter-button"
				>
					<AdjustmentsHorizontalSolid
						id="platform-list-filter-icon"
						class="h-5 w-5 {activeFiltersCount > 0 && !showFilterPanel ? 'text-white' : 'text-gray-600'}"
						data-testid="filter-icon"
					/>
				</button>

				<FilterPanel
					bind:isOpen={showFilterPanel}
					bind:filterData
					bind:activeFiltersCount
					{sortedIdentities}
					{unreadCounts}
					on:apply={handleFilterApply}
					on:close={handleFilterClose}
				/>
			</div>
		</div>
	</div>

	<!-- Tab Navigation -->
	<div
		id="platform-list-chat-tabs-container"
		class="flex-shrink-0 border-b border-gray-200 bg-white"
		data-testid="chat-tabs-container"
	>
		<nav id="platform-list-chat-tabs" class="flex w-full" data-testid="chat-tabs">
			{#each tabs as tab}
				<button
					id="platform-list-chat-tab-{tab.id}"
					on:click={() => handleTabClick(tab.id)}
					class="flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-center text-sm font-medium transition-colors
						{activeTab === tab.id
						? 'border-black bg-white text-black'
						: 'border-transparent text-gray-500 hover:text-gray-700'}"
					data-testid="chat-tab-{tab.id}"
					aria-selected={activeTab === tab.id}
					role="tab"
				>
					{t(tab.key)}
				</button>
			{/each}
		</nav>
	</div>

	<!-- Tab Content -->
	<div
		id="platform-list-chat-content-container"
		class="w-full flex-1 overflow-y-auto bg-gray-50"
		data-testid="chat-content-container"
	>
		<div
			id="platform-list-chat-content-wrapper"
			class="h-full w-full"
			data-testid="chat-content-wrapper"
		>
			<div
				id="platform-list-chat-content-scrollable"
				class="custom-scrollbar flex-1 overflow-y-auto"
				data-testid="platform-identity-list-content"
			>
				{#if sortedIdentities.length === 0}
					<div
						id="platform-list-empty-state"
						class="p-8 text-center text-gray-500"
						data-testid="empty-state"
					>
						{t('chat_center_filter_empty')}
					</div>
				{:else}
					<!-- Debug log before rendering keyed each block -->
					<!-- {(() => {
						console.log('[DEBUG] PlatformIdentityList: About to render keyed each block', {
							activeTab,
							sortedIdentitiesCount: sortedIdentities.length,
							keysForEachBlock: sortedIdentities.map(p => p.id),
							uniqueKeysCount: [...new Set(sortedIdentities.map(p => p.id))].length
						});

						// Final check for duplicate keys before rendering
						const keys = sortedIdentities.map(p => p.id);
						const uniqueKeys = [...new Set(keys)];
						if (keys.length !== uniqueKeys.length) {
							console.error('[CRITICAL ERROR] PlatformIdentityList: DUPLICATE KEYS DETECTED - KEYED EACH WILL FAIL!', {
								activeTab,
								allKeys: keys,
								duplicateKeys: keys.filter((key, index) => keys.indexOf(key) !== index),
								affectedPlatforms: sortedIdentities.filter((p, index) => keys.indexOf(p.id) !== index)
							});
						}
						return '';
					})()} -->

					<div
						id="platform-list-chat-items-list"
						class="divide-y divide-gray-100"
						data-testid="chat-items-list"
					>
						{#each sortedIdentities as identity (identity.id)}
							<button
								id="platform-list-chat-item-{identity.id}"
								class="chat-item relative w-full p-4 text-left transition-colors
									   {selectedPlatformId === identity.id
									? 'bg-blue-100 pl-6 hover:bg-blue-100'
									: 'hover:bg-gray-100'}"
								on:click={() => handleIdentityClick(identity)}
								data-testid="chat-item{selectedPlatformId === identity.id ? '-selected' : ''}"
								data-identity-id={identity.id}
								data-ticket-id={identity['latest_ticket_id']}
								data-customer-name={identity.display_name || identity.platform_username}
							>
								{#if selectedPlatformId === identity.id}
									<div
										id="platform-list-chat-item-selected-indicator-{identity.id}"
										class="absolute left-0 top-0 flex h-full w-1 items-center justify-center bg-blue-500"
										data-testid="chat-item-selected-indicator"
									></div>
								{/if}
								<div
									id="platform-list-chat-item-content-{identity.id}"
									class="flex items-start justify-between"
									data-testid="chat-item-content"
								>
									<!-- Avatar with Platform Icon Overlay -->
									<div
										id="platform-list-chat-item-avatar-container-{identity.id}"
										class="mr-3 flex-shrink-0"
										data-testid="chat-item-avatar-container"
									>
										<div
											id="platform-list-chat-item-avatar-wrapper-{identity.id}"
											class="relative"
											data-testid="chat-item-avatar-wrapper"
										>
											<!-- Main Avatar -->
											<div
												id="platform-list-chat-item-avatar-{identity.id}"
												class="h-12 w-12 overflow-hidden rounded-full bg-gray-100"
												data-testid="chat-item-avatar"
											>
												{#if isValidImageUrl(identity.platform_avatar_url) && !imageErrors.has(identity.platform_username)}
													<img
														id="platform-list-chat-item-avatar-image-{identity.id}"
														src={identity.platform_avatar_url}
														alt="{getCustomerName(identity)} avatar"
														class="h-full w-full rounded-full object-cover"
														on:error={() => handleImageError(identity.id)}
														data-testid="chat-item-avatar-image"
													/>
												{:else}
													<!-- Fallback initials when no picture_url or image failed to load -->
													<div
														id="platform-list-chat-item-avatar-initials-{identity.id}"
														class="flex h-full w-full items-center justify-center rounded-full bg-gray-100 font-medium text-gray-600"
														data-testid="chat-item-avatar-initials"
													>
														{getInitials(identity.platform_username)}
													</div>
												{/if}
											</div>
											<!-- Platform Icon Overlay -->
											<div
												id="platform-list-chat-item-platform-icon-container-{identity.id}"
												class="absolute -bottom-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full border border-white bg-white shadow-sm"
												data-testid="chat-item-platform-icon-container"
											>
												<img
													id="platform-list-chat-item-platform-icon-{identity.id}"
													src={getPlatformIcon(identity.platform)}
													alt="{identity.platform} icon"
													class="h-4 w-4 rounded-full object-cover"
													data-testid="chat-item-platform-icon"
												/>
											</div>
										</div>
									</div>
									<div
										id="platform-list-chat-item-info-{identity.id}"
										class="min-w-0 flex-1 pr-2"
										data-testid="chat-item-info"
									>
										<!-- Customer and Platform Info -->
										<div
											id="platform-list-chat-item-customer-info-{identity.id}"
											class="mb-1 flex items-center gap-2"
											data-testid="chat-item-customer-info"
										>
											<span
												id="platform-list-chat-item-customer-name-{identity.id}"
												class="truncate font-medium {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-900'}"
												data-testid="chat-item-customer-name"
											>
												{identity.display_name ||
													identity.platform_username ||
													identity.platform_user_id}
											</span>
										</div>

										<!-- Latest Message Preview -->
										{#if latestMessages.has(identity.id)}
											{@const message = latestMessages.get(identity.id)}
											<div
												id="platform-list-chat-item-latest-message-{identity.id}"
												class="mt-1 truncate text-sm {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-latest-message"
											>
												<div class="flex items-center">
													{#if message.is_self}
														<UserHeadsetOutline
															class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
																? 'text-blue-600'
																: 'text-gray-600'}"
														/>
													{/if}
													<span class="truncate">
														{#if message.message_type === 'ALTERNATIVE'}
															{t('chat_center_latest_message_is_template')}
														{:else}
															{message?.message}
														{/if}
													</span>
												 </div>
											</div>
										{:else if identity['last_message']}
											<div
												id="platform-list-chat-item-fallback-message-{identity.id}"
												class="mt-1 truncate text-sm {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-fallback-message"
											>
												{identity['last_message']}
											</div>
										{/if}
									</div>

									<!-- Right Side Info -->
									<div
										id="platform-list-chat-item-meta-{identity.id}"
										class="ml-2 flex flex-col items-end"
										data-testid="chat-item-meta"
									>
										<!-- Time -->
										{#if latestMessages.has(identity.id)}
											{@const message = latestMessages.get(identity.id)}
											<span
												id="platform-list-chat-item-time-{identity.id}"
												class="whitespace-nowrap text-xs {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-time"
											>
												{formatTime(message.created_on)}
											</span>
										{:else if identity['last_message_time']}
											<span
												id="platform-list-chat-item-fallback-time-{identity.id}"
												class="whitespace-nowrap text-xs {selectedPlatformId === identity.id
													? 'text-blue-800'
													: 'text-gray-600'}"
												data-testid="chat-item-fallback-time"
											>
												{formatTime(identity['last_message_time'])}
											</span>
										{/if}

										<!-- Unread Count -->
										{#if unreadCounts.get(identity.id) > 0}
											<span
												id="platform-list-chat-item-unread-count-{identity.id}"
												class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
														 bg-red-500 px-1.5 text-xs font-bold text-white"
												data-testid="chat-item-unread-count"
											>
												{unreadCounts.get(identity.id)}
											</span>
										{:else if identity.unread_count > 0}
											<span
												id="platform-list-chat-item-fallback-unread-count-{identity.id}"
												class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
														 bg-red-500 px-1.5 text-xs font-bold text-white"
												data-testid="chat-item-fallback-unread-count"
											>
												{identity.unread_count}
											</span>
										{/if}
									</div>
								</div>
								<div
									id="platform-list-chat-item-badges-{identity.id}"
									class="mt-4 flex-row items-center gap-1 space-y-1 text-xs text-gray-700"
									data-testid="chat-item-badges"
								>
									<!-- Ticket Badge -->
									<Badge
										id="platform-list-chat-item-ticket-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-ticket-badge"
									>
										<TicketSolid
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>
										{identity['latest_ticket_id']}
									</Badge>
									
									<!-- Priority Badge -->
									{#if identity['latest_ticket_priority']}
										<Badge
											id="platform-list-chat-item-priority-badge-{identity.id}"
											border
											color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
											data-testid="chat-item-priority-badge"
										>
											<ExclamationCircleOutline
												class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
													? 'text-blue-600'
													: 'text-gray-600'}"
											/>
											{t('tickets_priority_' + identity['latest_ticket_priority'].toLowerCase())}
										</Badge>
									{:else}
										<Badge
											id="platform-list-chat-item-priority-badge-{identity.id}"
											border
											color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
											data-testid="chat-item-priority-badge"
										>
											<ExclamationCircleOutline
												class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
													? 'text-blue-600'
													: 'text-gray-600'}"
											/>
											{t('tickets_priority_unknown')}
										</Badge>
									{/if}
									
									<!-- Owner Badge -->
									<Badge
										id="platform-list-chat-item-owner-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-owner-badge"
									>
										<UserHeadsetOutline
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>
										{identity['latest_ticket_owner']?.split(/\s+/)[0] ?? ''}
									</Badge>

									<!-- Channel Badge -->
									<Badge
										id="platform-list-chat-item-channel-badge-{identity.id}"
										border
										color={selectedPlatformId === identity.id ? 'blue' : 'gray'}
										data-testid="chat-item-channel-badge"
										><BullhornOutline
											class="mr-1 h-4 w-4 {selectedPlatformId === identity.id
												? 'text-blue-600'
												: 'text-gray-600'}"
										/>{identity.channel_name}</Badge
									>
									<!-- <Badge border color="gray">
										<TicketSolid class="mr-1 h-4 w-4 text-gray-600" />
										{identity['latest_ticket_priority']}
									</Badge> -->
									<!-- <Badge border color="gray">
										<TicketSolid class="mr-1 h-4 w-4 text-gray-600" />
										Status: {identity['latest_ticket_status']}
									</Badge> -->
								</div>
							</button>
						{/each}
					</div>

					<!-- Load More Button -->
					{#if hasMore}
						<div
							id="platform-list-load-more-container"
							data-testid="load-more-container"
							class="pt-4 border-t border-gray-200"
						>
							<Button
								on:click={handleLoadMore}
								disabled={loading}
								class="w-full hover:bg-gray-100 text-blue-600 focus:ring-0"
								color="none"
								size="sm"
								aria-label={loading ? t('chat_center_loading_more_conversations') : t('chat_center_load_more_conversations')}
								data-testid="load-more-button"
							>
								{#if loading}
									<div class="h-4 w-4 mr-2 animate-spin rounded-full border-b-2 border-blue-600"></div>
									{t('chat_center_loading_more_conversations')}
								{:else}
									<ArrowDownOutline class="mr-2 h-4 w-4" />
									{t('chat_center_load_more_conversations')}
								{/if}
							</Button>
						</div>
					{/if}
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.custom-scrollbar {
		scrollbar-width: thin;
		scrollbar-color: #e5e7eb #f9fafb;
	}

	.custom-scrollbar::-webkit-scrollbar {
		width: 6px;
	}

	.custom-scrollbar::-webkit-scrollbar-track {
		background: #f9fafb;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb {
		background-color: #e5e7eb;
		border-radius: 3px;
	}

	.custom-scrollbar::-webkit-scrollbar-thumb:hover {
		background-color: #d1d5db;
	}
</style>
