
// Dashboard ID mapping between frontend names and backend integer IDs
// This improve readability on frontend, inherit existing url to for downloading, and minimize payload size

export interface DashboardMapping {
    name: string;
    id: number;
    category: string;
    endpoint: string;
}

export const DASHBOARD_MAPPINGS: { [key: string]: DashboardMapping } = {
    // Agent Performance Tab
    individualPerformance: {
        name: 'individualPerformance',
        id: 1,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-performance-summary.xlsx/'
    },
    ticketsTransferred: {
        name: 'ticketsTransferred',
        id: 2,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-previous-assignment-count.xlsx/'
    },
    ticketsReceived: {
        name: 'ticketsReceived',
        id: 3,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-assigned-tickets-count.xlsx/'
    },
    responseRate: {
        name: 'responseRate',
        id: 4,
        category: 'agent_performance',
        endpoint: '/dashboard/api/agent-response-rate-within-5min.xlsx/'
    },
    overallPerformance: {
        name: 'overallPerformance',
        id: 5,
        category: 'agent_performance',
        endpoint: '/dashboard/api/comprehensive-agent-performance.xlsx/'
    },
    unclosedTickets: {
        name: 'unclosedTickets',
        id: 6,
        category: 'agent_performance',
        endpoint: '/dashboard/api/overdue-unclosed-tickets.xlsx/'
    },
    closedTickets: {
        name: 'closedTickets',
        id: 7,
        category: 'agent_performance',
        endpoint: '/dashboard/api/overdue-closed-tickets.xlsx/'
    },
    
    // Chat Performance Tab
    ticketsByStatus: {
        name: 'ticketsByStatus',
        id: 8,
        category: 'chat_performance',
        endpoint: '/dashboard/api/ticket-status-count.xlsx/'
    },
    ticketsByCaseType: {
        name: 'ticketsByCaseType',
        id: 9,
        category: 'chat_performance',
        endpoint: '/dashboard/api/closed-tickets-by-case-type.xlsx/'
    },
    ticketsBySubCaseType: {
        name: 'ticketsBySubCaseType',
        id: 10,
        category: 'chat_performance',
        endpoint: '/dashboard/api/closed-tickets-by-case-topic.xlsx/'
    },
    caseSubCaseTable: {
        name: 'caseSubCaseTable',
        id: 11,
        category: 'chat_performance',
        endpoint: '/dashboard/api/closed-tickets-by-case-type-and-topic.xlsx/'
    },
    
    // Response Time Volume Tab
    dailyVolume: {
        name: 'dailyVolume',
        id: 12,
        category: 'response_time_volume',
        endpoint: '/dashboard/api/incoming-message-count-time-series.xlsx/'
    },
    messagesByTimeSlot: {
        name: 'messagesByTimeSlot',
        id: 13,
        category: 'response_time_volume',
        endpoint: '/dashboard/api/customer-message-heatmap.xlsx/'
    },
    
    // Work Quality Tab
    agentChatbotComparison: {
        name: 'agentChatbotComparison',
        id: 14,
        category: 'work_quality',
        endpoint: '/dashboard/api/responder-response-time.xlsx/'
    },
    dailyCsat: {
        name: 'dailyCsat',
        id: 15,
        category: 'work_quality',
        endpoint: '/dashboard/api/csat-score-time-series.xlsx/'
    },
    dailyFirstResponseTime: {
        name: 'dailyFirstResponseTime',
        id: 16,
        category: 'work_quality',
        endpoint: '/dashboard/api/first-response-time.xlsx/'
    },
    dailyResponseTime: {
        name: 'dailyResponseTime',
        id: 17,
        category: 'work_quality',
        endpoint: '/dashboard/api/responder-response-time.xlsx/'
    },
    overallSentiment: {
        name: 'overallSentiment',
        id: 18,
        category: 'work_quality',
        endpoint: '/dashboard/api/sentiment-analysis-summary.xlsx/'
    },
    dailySentiment: {
        name: 'dailySentiment',
        id: 19,
        category: 'work_quality',
        endpoint: '/dashboard/api/sentiment-analysis-time-series.xlsx/'
    },
    caseTypeSentiment: {
        name: 'caseTypeSentiment',
        id: 20,
        category: 'work_quality',
        endpoint: '/dashboard/api/sentiment-analysis-by-case-type.xlsx/'
    },
    
    // Customer Satisfaction Tab
    csatTickets: {
        name: 'csatTickets',
        id: 21,
        category: 'customer_satisfaction',
        endpoint: '/dashboard/api/closed-tickets-with-csat.xlsx/' 
    }
};

export function getDashboardId(name: string): number | null {
    return DASHBOARD_MAPPINGS[name]?.id || null;
}

export function getDashboardName(id: number): string | null {
    const entry = Object.values(DASHBOARD_MAPPINGS).find(mapping => mapping.id === id);
    return entry?.name || null;
}

export function getAllDashboardIds(): number[] {
    return Object.values(DASHBOARD_MAPPINGS).map(mapping => mapping.id);
}

export function getAllDashboardNames(): string[] {
    return Object.keys(DASHBOARD_MAPPINGS);
}

export function convertToBackendIds(selectedDashboards: { [key: string]: boolean }): number[] {
    return Object.entries(selectedDashboards)
        .filter(([, selected]) => selected)
        .map(([name]) => getDashboardId(name))
        .filter((id): id is number => id !== null);
}

export function convertToFrontendSelection(dashboardIds: number[]): { [key: string]: boolean } {
    const result: { [key: string]: boolean } = {};
    
    // Initialize all dashboards to false
    Object.keys(DASHBOARD_MAPPINGS).forEach(name => {
        result[name] = false;
    });
    
    // Set selected ones to true
    dashboardIds.forEach(id => {
        const name = getDashboardName(id);
        if (name) {
            result[name] = true;
        }
    });
    
    return result;
}
